import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head />
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}

// Override getInitialProps to prevent styled-jsx injection
Document.getInitialProps = async (ctx: any) => {
  const originalRenderPage = ctx.renderPage;

  ctx.renderPage = () =>
    originalRenderPage({
      enhanceApp: (App: any) => (props: any) => {
        // Wrap with our custom style provider instead of styled-jsx
        return <App {...props} />;
      },
    });

  const initialProps = await ctx.defaultGetInitialProps(ctx);

  return {
    ...initialProps,
    // Remove any styled-jsx styles
    styles: null,
  };
};
